'use client';

import { useState } from 'react';
import { generateIdNumber } from '@/lib/id-utils';

interface IdGeneratorProps {
  onIdGenerated?: (idNumber: string) => void;
}

export default function IdGenerator({ onIdGenerated }: IdGeneratorProps) {
  const [dateOfBirth, setDateOfBirth] = useState('');
  const [gender, setGender] = useState<'male' | 'female'>('male');
  const [isCitizen, setIsCitizen] = useState(true);
  const [generatedId, setGeneratedId] = useState('');
  const [error, setError] = useState('');

  const handleGenerate = () => {
    try {
      setError('');

      // Convert date input (YYYY-MM-DD) to YYMMDD format
      if (!dateOfBirth) {
        setError('Please select a date of birth');
        return;
      }

      const dateParts = dateOfBirth.split('-');
      if (dateParts.length !== 3) {
        setError('Invalid date format');
        return;
      }

      // Validate date is not in the future
      const selectedDate = new Date(dateOfBirth);
      const today = new Date();
      if (selectedDate > today) {
        setError('Date of birth cannot be in the future');
        return;
      }

      // Validate date is not too far in the past (reasonable limit)
      const minDate = new Date();
      minDate.setFullYear(minDate.getFullYear() - 120);
      if (selectedDate < minDate) {
        setError('Date of birth cannot be more than 120 years ago');
        return;
      }

      const year = dateParts[0].substring(2); // Get last 2 digits of year
      const month = dateParts[1];
      const day = dateParts[2];
      const formattedDate = `${year}${month}${day}`;

      // Generate ID number
      const idNumber = generateIdNumber(formattedDate, gender, isCitizen);
      setGeneratedId(idNumber);

      // Call the callback if provided
      if (onIdGenerated) {
        onIdGenerated(idNumber);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while generating the ID number');
    }
  };

  const handleCopy = () => {
    if (generatedId) {
      navigator.clipboard.writeText(generatedId)
        .then(() => {
          // Could add a toast notification here
        })
        .catch(err => {
          console.error('Failed to copy: ', err);
        });
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">Generate South African ID Number</h2>
      
      <div className="space-y-4">
        <div>
          <label htmlFor="dateOfBirth" className="block text-sm font-medium mb-1">
            Date of Birth
          </label>
          <input
            type="date"
            id="dateOfBirth"
            value={dateOfBirth}
            onChange={(e) => setDateOfBirth(e.target.value)}
            className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-1">Gender</label>
          <div className="flex space-x-4">
            <label className="inline-flex items-center">
              <input
                type="radio"
                name="gender"
                checked={gender === 'male'}
                onChange={() => setGender('male')}
                className="mr-2"
              />
              Male
            </label>
            <label className="inline-flex items-center">
              <input
                type="radio"
                name="gender"
                checked={gender === 'female'}
                onChange={() => setGender('female')}
                className="mr-2"
              />
              Female
            </label>
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium mb-1">Citizenship</label>
          <div className="flex space-x-4">
            <label className="inline-flex items-center">
              <input
                type="radio"
                name="citizenship"
                checked={isCitizen}
                onChange={() => setIsCitizen(true)}
                className="mr-2"
              />
              South African Citizen
            </label>
            <label className="inline-flex items-center">
              <input
                type="radio"
                name="citizenship"
                checked={!isCitizen}
                onChange={() => setIsCitizen(false)}
                className="mr-2"
              />
              Permanent Resident
            </label>
          </div>
        </div>
        
        <button
          onClick={handleGenerate}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
        >
          Generate ID Number
        </button>
        
        {error && (
          <div className="text-red-500 text-sm mt-2">
            {error}
          </div>
        )}
        
        {generatedId && (
          <div className="mt-4">
            <div className="flex justify-between items-center">
              <label className="block text-sm font-medium">Generated ID Number:</label>
              <button
                onClick={handleCopy}
                className="text-xs bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 px-2 py-1 rounded"
              >
                Copy
              </button>
            </div>
            <div className="p-3 bg-gray-100 dark:bg-gray-900 rounded-md font-mono text-lg mt-1">
              {generatedId}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
