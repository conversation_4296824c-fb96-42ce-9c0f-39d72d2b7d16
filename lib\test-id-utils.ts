/**
 * Test functions for South African ID Number utilities
 * This file contains test cases to verify the correctness of our ID generation and validation logic
 */

import { 
  generateIdNumber, 
  validateIdNumber, 
  extractIdInformation, 
  calculateLuhnDigit,
  isValidDate 
} from './id-utils';

/**
 * Test the Luhn algorithm calculation
 */
export function testLuhnAlgorithm(): boolean {
  console.log('Testing Luhn Algorithm...');
  
  // Test case from the documentation: ID number 8501016184086
  // First 12 digits: 850101618408
  const testId = '850101618408';
  const expectedCheckDigit = 6;
  const calculatedCheckDigit = calculateLuhnDigit(testId);
  
  console.log(`Expected check digit: ${expectedCheckDigit}`);
  console.log(`Calculated check digit: ${calculatedCheckDigit}`);
  
  const passed = calculatedCheckDigit === expectedCheckDigit;
  console.log(`Luhn test ${passed ? 'PASSED' : 'FAILED'}`);
  
  return passed;
}

/**
 * Test date validation
 */
export function testDateValidation(): boolean {
  console.log('\nTesting Date Validation...');
  
  const testCases = [
    { date: '850101', expected: true, description: 'Valid date: 1985-01-01' },
    { date: '991231', expected: true, description: 'Valid date: 1999-12-31' },
    { date: '000229', expected: true, description: 'Valid leap year: 2000-02-29' },
    { date: '010229', expected: false, description: 'Invalid leap year: 2001-02-29' },
    { date: '851301', expected: false, description: 'Invalid month: 13' },
    { date: '850132', expected: false, description: 'Invalid day: 32' },
    { date: '85010', expected: false, description: 'Too short' },
    { date: '8501011', expected: false, description: 'Too long' },
    { date: 'abcdef', expected: false, description: 'Non-numeric' }
  ];
  
  let allPassed = true;
  
  testCases.forEach(testCase => {
    const result = isValidDate(testCase.date);
    const passed = result === testCase.expected;
    console.log(`${testCase.description}: ${passed ? 'PASSED' : 'FAILED'} (expected: ${testCase.expected}, got: ${result})`);
    if (!passed) allPassed = false;
  });
  
  return allPassed;
}

/**
 * Test ID number generation
 */
export function testIdGeneration(): boolean {
  console.log('\nTesting ID Generation...');
  
  try {
    // Generate a few test IDs
    const testCases = [
      { date: '850101', gender: 'male' as const, citizen: true },
      { date: '990615', gender: 'female' as const, citizen: false },
      { date: '001225', gender: 'male' as const, citizen: true }
    ];
    
    let allPassed = true;
    
    testCases.forEach((testCase, index) => {
      try {
        const generatedId = generateIdNumber(testCase.date, testCase.gender, testCase.citizen);
        console.log(`Test ${index + 1}: Generated ID: ${generatedId}`);
        
        // Validate the generated ID
        const isValid = validateIdNumber(generatedId);
        console.log(`  Validation: ${isValid ? 'PASSED' : 'FAILED'}`);
        
        // Extract information and verify it matches input
        const info = extractIdInformation(generatedId);
        
        if (info.isValid) {
          const genderMatch = info.gender === testCase.gender;
          const citizenMatch = info.isCitizen === testCase.citizen;
          
          console.log(`  Gender match: ${genderMatch ? 'PASSED' : 'FAILED'} (expected: ${testCase.gender}, got: ${info.gender})`);
          console.log(`  Citizenship match: ${citizenMatch ? 'PASSED' : 'FAILED'} (expected: ${testCase.citizen}, got: ${info.isCitizen})`);
          
          if (!isValid || !genderMatch || !citizenMatch) {
            allPassed = false;
          }
        } else {
          console.log('  Failed to extract information from generated ID');
          allPassed = false;
        }
      } catch (error) {
        console.log(`Test ${index + 1} FAILED with error: ${error}`);
        allPassed = false;
      }
    });
    
    return allPassed;
  } catch (error) {
    console.log(`ID Generation test failed: ${error}`);
    return false;
  }
}

/**
 * Test ID validation with known valid and invalid IDs
 */
export function testIdValidation(): boolean {
  console.log('\nTesting ID Validation...');
  
  const testCases = [
    { id: '8501016184086', expected: true, description: 'Known valid ID from documentation' },
    { id: '1234567890123', expected: false, description: 'Invalid check digit' },
    { id: '8513016184086', expected: false, description: 'Invalid month (13)' },
    { id: '8501326184086', expected: false, description: 'Invalid day (32)' },
    { id: '85010161840', expected: false, description: 'Too short' },
    { id: '85010161840861', expected: false, description: 'Too long' },
    { id: 'abcdefghijklm', expected: false, description: 'Non-numeric' },
    { id: '', expected: false, description: 'Empty string' }
  ];
  
  let allPassed = true;
  
  testCases.forEach(testCase => {
    const result = validateIdNumber(testCase.id);
    const passed = result === testCase.expected;
    console.log(`${testCase.description}: ${passed ? 'PASSED' : 'FAILED'} (expected: ${testCase.expected}, got: ${result})`);
    if (!passed) allPassed = false;
  });
  
  return allPassed;
}

/**
 * Test information extraction
 */
export function testInformationExtraction(): boolean {
  console.log('\nTesting Information Extraction...');
  
  // Using the known valid ID from documentation: 8501016184086
  const testId = '8501016184086';
  const info = extractIdInformation(testId);
  
  if (!info.isValid) {
    console.log('FAILED: Could not extract information from known valid ID');
    return false;
  }
  
  console.log('Extracted information:');
  console.log(`  Date of Birth: ${info.dateOfBirth?.toLocaleDateString()}`);
  console.log(`  Age: ${info.age}`);
  console.log(`  Gender: ${info.gender}`);
  console.log(`  Citizenship: ${info.isCitizen ? 'SA Citizen' : 'Permanent Resident'}`);
  console.log(`  Sequence Number: ${info.sequenceNumber}`);
  
  // Verify expected values
  const expectedGender = 'male'; // 6 is >= 5, so male
  const expectedCitizen = true; // 0 means SA citizen
  
  const genderCorrect = info.gender === expectedGender;
  const citizenCorrect = info.isCitizen === expectedCitizen;
  
  console.log(`Gender extraction: ${genderCorrect ? 'PASSED' : 'FAILED'}`);
  console.log(`Citizenship extraction: ${citizenCorrect ? 'PASSED' : 'FAILED'}`);
  
  return genderCorrect && citizenCorrect;
}

/**
 * Run all tests
 */
export function runAllTests(): boolean {
  console.log('=== Running South African ID Number Tests ===\n');
  
  const tests = [
    testLuhnAlgorithm,
    testDateValidation,
    testIdGeneration,
    testIdValidation,
    testInformationExtraction
  ];
  
  let allTestsPassed = true;
  
  tests.forEach(test => {
    const passed = test();
    if (!passed) {
      allTestsPassed = false;
    }
    console.log(''); // Add spacing between tests
  });
  
  console.log('=== Test Summary ===');
  console.log(`Overall result: ${allTestsPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
  
  return allTestsPassed;
}
