'use client';

import { useState } from 'react';
import IdGenerator from '@/components/IdGenerator';
import IdValidator from '@/components/IdValidator';
import IdExplanation from '@/components/IdExplanation';
import TestRunner from '@/components/TestRunner';

export default function Home() {
  const [activeTab, setActiveTab] = useState<'generate' | 'validate' | 'explain' | 'test'>('generate');
  const [currentIdNumber, setCurrentIdNumber] = useState('');

  const handleIdGenerated = (idNumber: string) => {
    setCurrentIdNumber(idNumber);
  };

  const tabs = [
    { id: 'generate', label: 'Generate ID', icon: '🔢' },
    { id: 'validate', label: 'Validate ID', icon: '✅' },
    { id: 'explain', label: 'ID Structure', icon: '📖' },
    { id: 'test', label: 'Run Tests', icon: '🧪' }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            South African ID Number Generator
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Generate, validate, and understand South African ID numbers for testing purposes
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-1 shadow-md">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`px-4 py-2 rounded-md font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <span className="mr-2">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        <div className="max-w-4xl mx-auto">
          {activeTab === 'generate' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <IdGenerator onIdGenerated={handleIdGenerated} />
              <IdExplanation idNumber={currentIdNumber} />
            </div>
          )}

          {activeTab === 'validate' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <IdValidator initialIdNumber={currentIdNumber} />
              <IdExplanation idNumber={currentIdNumber} />
            </div>
          )}

          {activeTab === 'explain' && (
            <IdExplanation idNumber={currentIdNumber} />
          )}

          {activeTab === 'test' && (
            <TestRunner />
          )}
        </div>

        {/* Footer */}
        <footer className="mt-16 text-center text-sm text-gray-500 dark:text-gray-400">
          <p>
            This tool is for testing purposes only. Do not use for actual identity verification.
          </p>
          <p className="mt-2">
            Based on the official South African ID number format and validation rules.
          </p>
        </footer>
      </div>
    </div>
  );
}
