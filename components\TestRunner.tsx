'use client';

import { useState } from 'react';
import { 
  runAllTests, 
  testLuhnAlgorithm, 
  testDateValidation, 
  testIdGeneration, 
  testIdValidation, 
  testInformationExtraction 
} from '@/lib/test-id-utils';

export default function TestRunner() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  
  const captureConsoleLog = (callback: () => boolean) => {
    const originalConsoleLog = console.log;
    const logs: string[] = [];
    
    // Override console.log to capture output
    console.log = (...args) => {
      logs.push(args.join(' '));
    };
    
    try {
      // Run the test
      const result = callback();
      
      // Add overall result
      logs.push(`\nTest result: ${result ? 'PASSED' : 'FAILED'}`);
      
      return logs;
    } finally {
      // Restore original console.log
      console.log = originalConsoleLog;
    }
  };
  
  const runTest = (testFn: () => boolean, testName: string) => {
    setIsRunning(true);
    setTestResults([`Running ${testName}...`]);
    
    // Use setTimeout to allow UI to update
    setTimeout(() => {
      try {
        const logs = captureConsoleLog(testFn);
        setTestResults(logs);
      } catch (error) {
        setTestResults([`Error running ${testName}: ${error}`]);
      } finally {
        setIsRunning(false);
      }
    }, 100);
  };
  
  const tests = [
    { name: 'All Tests', fn: runAllTests },
    { name: 'Luhn Algorithm', fn: testLuhnAlgorithm },
    { name: 'Date Validation', fn: testDateValidation },
    { name: 'ID Generation', fn: testIdGeneration },
    { name: 'ID Validation', fn: testIdValidation },
    { name: 'Information Extraction', fn: testInformationExtraction }
  ];
  
  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">Test South African ID Number Logic</h2>
      
      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 mb-6">
        {tests.map((test) => (
          <button
            key={test.name}
            onClick={() => runTest(test.fn, test.name)}
            disabled={isRunning}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-md transition-colors text-sm"
          >
            Run {test.name}
          </button>
        ))}
      </div>
      
      {testResults.length > 0 && (
        <div className="mt-4">
          <h3 className="font-medium mb-2">Test Results:</h3>
          <div className="bg-gray-100 dark:bg-gray-900 p-4 rounded-md font-mono text-sm overflow-auto max-h-96">
            {testResults.map((line, index) => (
              <div key={index} className={`
                ${line.includes('PASSED') ? 'text-green-600 dark:text-green-400' : ''}
                ${line.includes('FAILED') ? 'text-red-600 dark:text-red-400' : ''}
                ${line.startsWith('===') ? 'font-bold' : ''}
              `}>
                {line}
              </div>
            ))}
          </div>
        </div>
      )}
      
      <div className="mt-6 text-sm text-gray-600 dark:text-gray-400">
        <p>
          These tests verify the correctness of the ID number generation and validation logic.
          Run them to ensure all components of the system are working as expected.
        </p>
      </div>
    </div>
  );
}
