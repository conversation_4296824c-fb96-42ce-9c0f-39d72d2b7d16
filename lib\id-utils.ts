/**
 * South African ID Number Utilities
 * 
 * This module provides functions for generating, validating, and extracting information
 * from South African ID numbers based on the official format:
 * 
 * Format: YYMMDD GSSS C A Z
 * - YYMMDD: Date of birth
 * - G: Gender (0-4 Female, 5-9 Male)
 * - SSS: Sequence number
 * - C: Citizenship (0 SA Citizen, 1 Permanent Resident)
 * - A: Usually 8 (historically used for race classification)
 * - Z: Check digit (Luhn algorithm)
 */

/**
 * Calculates the Luhn algorithm check digit for a South African ID number
 * @param idNumberWithoutCheckDigit - The first 12 digits of the ID number
 * @returns The check digit (0-9)
 */
export function calculateLuhnDigit(idNumberWithoutCheckDigit: string): number {
  let sum = 0;
  let alternate = false;
  
  // Process from right to left as per the algorithm
  for (let i = idNumberWithoutCheckDigit.length - 1; i >= 0; i--) {
    let digit = parseInt(idNumberWithoutCheckDigit[i], 10);
    
    if (alternate) {
      digit *= 2;
      if (digit > 9) {
        digit = Math.floor(digit / 10) + (digit % 10);
      }
    }
    
    sum += digit;
    alternate = !alternate;
  }
  
  // Calculate check digit: (sum * 9) % 10
  return (sum * 9) % 10;
}

/**
 * Validates if a date string in YYMMDD format is a valid date
 * @param dateStr - Date string in YYMMDD format
 * @returns Boolean indicating if the date is valid
 */
export function isValidDate(dateStr: string): boolean {
  if (dateStr.length !== 6 || !/^\d{6}$/.test(dateStr)) {
    return false;
  }
  
  const year = parseInt(dateStr.substring(0, 2), 10);
  const month = parseInt(dateStr.substring(2, 4), 10);
  const day = parseInt(dateStr.substring(4, 6), 10);
  
  // Check month range
  if (month < 1 || month > 12) {
    return false;
  }
  
  // Determine century (19xx or 20xx)
  const currentYear = new Date().getFullYear() % 100;
  const fullYear = year <= currentYear ? 2000 + year : 1900 + year;
  
  // Check day range based on month
  const daysInMonth = new Date(fullYear, month, 0).getDate();
  if (day < 1 || day > daysInMonth) {
    return false;
  }
  
  return true;
}

/**
 * Generates a random sequence number (3 digits)
 * @returns A 3-digit string with leading zeros if needed
 */
export function generateSequenceNumber(): string {
  const sequenceNumber = Math.floor(Math.random() * 1000);
  return sequenceNumber.toString().padStart(3, '0');
}

/**
 * Generates a valid South African ID number based on provided parameters
 * @param dateOfBirth - Date of birth in YYMMDD format
 * @param gender - 'male' or 'female'
 * @param isCitizen - Whether the person is a South African citizen
 * @returns A valid 13-digit South African ID number
 */
export function generateIdNumber(
  dateOfBirth: string,
  gender: 'male' | 'female',
  isCitizen: boolean
): string {
  // Validate date format
  if (!isValidDate(dateOfBirth)) {
    throw new Error('Invalid date format. Must be YYMMDD and a valid date.');
  }
  
  // Generate gender digit (0-4 for female, 5-9 for male)
  const genderDigit = gender === 'female' 
    ? Math.floor(Math.random() * 5) 
    : 5 + Math.floor(Math.random() * 5);
  
  // Generate sequence number
  const sequenceNumber = generateSequenceNumber();
  
  // Citizenship digit (0 for SA citizen, 1 for permanent resident)
  const citizenshipDigit = isCitizen ? '0' : '1';
  
  // Use 8 for the "A" digit (historically used for race)
  const aDigit = '8';
  
  // Combine the first 12 digits
  const idWithoutCheckDigit = `${dateOfBirth}${genderDigit}${sequenceNumber}${citizenshipDigit}${aDigit}`;
  
  // Calculate the check digit using Luhn algorithm
  const checkDigit = calculateLuhnDigit(idWithoutCheckDigit);
  
  // Return the complete 13-digit ID number
  return `${idWithoutCheckDigit}${checkDigit}`;
}

/**
 * Validates a South African ID number
 * @param idNumber - The 13-digit ID number to validate
 * @returns Boolean indicating if the ID number is valid
 */
export function validateIdNumber(idNumber: string): boolean {
  // Check if the ID number is exactly 13 digits
  if (!idNumber || idNumber.length !== 13 || !/^\d{13}$/.test(idNumber)) {
    return false;
  }

  // Extract the date portion and validate it
  const datePortion = idNumber.substring(0, 6);
  if (!isValidDate(datePortion)) {
    return false;
  }

  // Extract the first 12 digits and calculate the expected check digit
  const idWithoutCheckDigit = idNumber.substring(0, 12);
  const expectedCheckDigit = calculateLuhnDigit(idWithoutCheckDigit);
  const actualCheckDigit = parseInt(idNumber[12], 10);

  // Verify the check digit matches
  return expectedCheckDigit === actualCheckDigit;
}

/**
 * Validates ID number format and returns detailed validation results
 * @param idNumber - The ID number to validate
 * @returns Object with validation details
 */
export function validateIdNumberDetailed(idNumber: string): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check length and format
  if (!idNumber) {
    errors.push('ID number is required');
    return { isValid: false, errors, warnings };
  }

  if (idNumber.length !== 13) {
    errors.push(`ID number must be exactly 13 digits (current length: ${idNumber.length})`);
  }

  if (!/^\d+$/.test(idNumber)) {
    errors.push('ID number must contain only digits');
  }

  if (errors.length > 0) {
    return { isValid: false, errors, warnings };
  }

  // Validate date portion
  const datePortion = idNumber.substring(0, 6);
  if (!isValidDate(datePortion)) {
    errors.push('Invalid date in ID number');
  }

  // Validate check digit
  const idWithoutCheckDigit = idNumber.substring(0, 12);
  const expectedCheckDigit = calculateLuhnDigit(idWithoutCheckDigit);
  const actualCheckDigit = parseInt(idNumber[12], 10);

  if (expectedCheckDigit !== actualCheckDigit) {
    errors.push(`Invalid check digit. Expected: ${expectedCheckDigit}, Got: ${actualCheckDigit}`);
  }

  // Check for unusual values (warnings)
  const aDigit = idNumber[10];
  if (aDigit !== '8' && aDigit !== '9') {
    warnings.push(`Unusual value in position 11: ${aDigit} (expected 8 or 9)`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Interface for extracted ID information
 */
export interface IdInformation {
  isValid: boolean;
  dateOfBirth?: Date;
  age?: number;
  gender?: 'male' | 'female';
  isCitizen?: boolean;
  sequenceNumber?: string;
}

/**
 * Extracts information from a South African ID number
 * @param idNumber - The 13-digit ID number
 * @returns Object containing extracted information
 */
export function extractIdInformation(idNumber: string): IdInformation {
  // First validate the ID number
  if (!validateIdNumber(idNumber)) {
    return { isValid: false };
  }

  // Extract date of birth
  const dateStr = idNumber.substring(0, 6);
  const year = parseInt(dateStr.substring(0, 2), 10);
  const month = parseInt(dateStr.substring(2, 4), 10);
  const day = parseInt(dateStr.substring(4, 6), 10);

  // Determine century (19xx or 20xx)
  const currentYear = new Date().getFullYear() % 100;
  const fullYear = year <= currentYear ? 2000 + year : 1900 + year;

  const dateOfBirth = new Date(fullYear, month - 1, day); // month is 0-indexed in Date constructor

  // Calculate age
  const today = new Date();
  let age = today.getFullYear() - dateOfBirth.getFullYear();
  const monthDiff = today.getMonth() - dateOfBirth.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dateOfBirth.getDate())) {
    age--;
  }

  // Extract gender (7th digit: 0-4 female, 5-9 male)
  const genderDigit = parseInt(idNumber[6], 10);
  const gender: 'male' | 'female' = genderDigit < 5 ? 'female' : 'male';

  // Extract sequence number (8th-10th digits)
  const sequenceNumber = idNumber.substring(7, 10);

  // Extract citizenship (11th digit: 0 SA citizen, 1 permanent resident)
  const citizenshipDigit = parseInt(idNumber[10], 10);
  const isCitizen = citizenshipDigit === 0;

  return {
    isValid: true,
    dateOfBirth,
    age,
    gender,
    isCitizen,
    sequenceNumber
  };
}

/**
 * Formats a date string from YYMMDD to a more readable format
 * @param dateStr - Date string in YYMMDD format
 * @returns Formatted date string or null if invalid
 */
export function formatDateFromIdNumber(dateStr: string): string | null {
  if (!isValidDate(dateStr)) {
    return null;
  }

  const year = parseInt(dateStr.substring(0, 2), 10);
  const month = parseInt(dateStr.substring(2, 4), 10);
  const day = parseInt(dateStr.substring(4, 6), 10);

  // Determine century
  const currentYear = new Date().getFullYear() % 100;
  const fullYear = year <= currentYear ? 2000 + year : 1900 + year;

  const date = new Date(fullYear, month - 1, day);
  return date.toLocaleDateString('en-ZA', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}
