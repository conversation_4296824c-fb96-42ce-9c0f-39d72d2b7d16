'use client';

interface IdExplanationProps {
  idNumber?: string;
}

export default function IdExplanation({ idNumber }: IdExplanationProps) {
  // Define the structure of the ID number
  const idStructure = [
    { name: 'Date of Birth (YYMMDD)', length: 6, description: 'Year, month, and day of birth' },
    { name: 'Gender', length: 1, description: '0-4: Female, 5-9: Male' },
    { name: 'Sequence Number', length: 3, description: 'Random number for people born on the same day' },
    { name: 'Citizenship', length: 1, description: '0: SA Citizen, 1: Permanent Resident' },
    { name: 'A Digit', length: 1, description: 'Usually 8 (historically used for race classification)' },
    { name: 'Check Digit', length: 1, description: 'Calculated using <PERSON>hn algorithm' }
  ];

  // Function to highlight parts of the ID number
  const highlightIdParts = () => {
    if (!idNumber || idNumber.length !== 13) {
      return null;
    }

    let startIndex = 0;
    return (
      <div className="font-mono text-lg mb-4 flex flex-wrap">
        {idStructure.map((part, index) => {
          const segment = idNumber.substring(startIndex, startIndex + part.length);
          const result = (
            <span 
              key={index} 
              className={`inline-block px-1 py-0.5 m-0.5 rounded ${getColorForPart(index)}`}
              title={part.description}
            >
              {segment}
            </span>
          );
          startIndex += part.length;
          return result;
        })}
      </div>
    );
  };

  // Function to get color class for each part
  const getColorForPart = (index: number): string => {
    const colors = [
      'bg-blue-100 dark:bg-blue-900/30',
      'bg-green-100 dark:bg-green-900/30',
      'bg-yellow-100 dark:bg-yellow-900/30',
      'bg-purple-100 dark:bg-purple-900/30',
      'bg-pink-100 dark:bg-pink-900/30',
      'bg-red-100 dark:bg-red-900/30'
    ];
    return colors[index % colors.length];
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">South African ID Number Structure</h2>
      
      {idNumber && idNumber.length === 13 && (
        <div className="mb-6">
          <h3 className="text-sm font-medium mb-2">Your ID Number Breakdown:</h3>
          {highlightIdParts()}
        </div>
      )}
      
      <div className="space-y-4">
        <p className="text-sm">
          South African ID numbers follow a specific format that encodes personal information:
        </p>
        
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm">
            <thead>
              <tr className="bg-gray-100 dark:bg-gray-700">
                <th className="px-4 py-2 text-left">Position</th>
                <th className="px-4 py-2 text-left">Component</th>
                <th className="px-4 py-2 text-left">Description</th>
              </tr>
            </thead>
            <tbody>
              {idStructure.map((part, index) => {
                const startPos = index === 0 ? 1 : 
                  idStructure.slice(0, index).reduce((sum, p) => sum + p.length, 1);
                const endPos = startPos + part.length - 1;
                
                return (
                  <tr key={index} className="border-b dark:border-gray-700">
                    <td className="px-4 py-2">
                      {startPos === endPos ? startPos : `${startPos}-${endPos}`}
                    </td>
                    <td className="px-4 py-2 font-medium">{part.name}</td>
                    <td className="px-4 py-2">{part.description}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
        
        <div className="mt-4 text-sm">
          <h3 className="font-medium mb-2">Luhn Algorithm (Check Digit)</h3>
          <p>
            The last digit is a check digit calculated using the Luhn algorithm, which helps detect errors in the ID number.
            This algorithm works by doubling every second digit (starting from the right), summing all digits, 
            multiplying by 9, and taking the result modulo 10.
          </p>
        </div>
      </div>
    </div>
  );
}
