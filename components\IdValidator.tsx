'use client';

import { useState, useEffect } from 'react';
import { validateIdNumberDetailed, extractIdInformation, IdInformation } from '@/lib/id-utils';

interface IdValidatorProps {
  initialIdNumber?: string;
}

export default function IdValidator({ initialIdNumber = '' }: IdValidatorProps) {
  const [idNumber, setIdNumber] = useState(initialIdNumber);
  const [validationResult, setValidationResult] = useState<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } | null>(null);
  const [idInfo, setIdInfo] = useState<IdInformation | null>(null);

  useEffect(() => {
    if (initialIdNumber) {
      setIdNumber(initialIdNumber);
      validateId(initialIdNumber);
    }
  }, [initialIdNumber]);

  const validateId = (id: string) => {
    if (!id || id.length === 0) {
      setValidationResult(null);
      setIdInfo(null);
      return;
    }

    const result = validateIdNumberDetailed(id);
    setValidationResult(result);

    if (result.isValid) {
      const info = extractIdInformation(id);
      setIdInfo(info);
    } else {
      setIdInfo(null);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    
    // Only allow digits and limit to 13 characters
    if (/^\d*$/.test(value) && value.length <= 13) {
      setIdNumber(value);
      
      // Only validate if we have all 13 digits
      if (value.length === 13) {
        validateId(value);
      } else {
        setValidationResult(null);
        setIdInfo(null);
      }
    }
  };

  const handleValidate = () => {
    validateId(idNumber);
  };

  return (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">Validate South African ID Number</h2>
      
      <div className="space-y-4">
        <div>
          <label htmlFor="idNumber" className="block text-sm font-medium mb-1">
            ID Number
          </label>
          <input
            type="text"
            id="idNumber"
            value={idNumber}
            onChange={handleInputChange}
            placeholder="Enter 13-digit ID number"
            className="w-full p-2 border rounded-md dark:bg-gray-700 dark:border-gray-600"
          />
        </div>
        
        <button
          onClick={handleValidate}
          disabled={!idNumber || idNumber.length !== 13}
          className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-md transition-colors"
        >
          Validate ID Number
        </button>
        
        {validationResult && (
          <div className="mt-4">
            <div className={`p-3 rounded-md ${validationResult.isValid ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200' : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200'}`}>
              <p className="font-medium">
                {validationResult.isValid ? 'Valid ID Number' : 'Invalid ID Number'}
              </p>
              
              {validationResult.errors.length > 0 && (
                <div className="mt-2">
                  <p className="font-medium text-sm">Errors:</p>
                  <ul className="list-disc list-inside text-sm">
                    {validationResult.errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              )}
              
              {validationResult.warnings.length > 0 && (
                <div className="mt-2">
                  <p className="font-medium text-sm">Warnings:</p>
                  <ul className="list-disc list-inside text-sm">
                    {validationResult.warnings.map((warning, index) => (
                      <li key={index}>{warning}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}
        
        {idInfo && idInfo.isValid && (
          <div className="mt-4 p-4 bg-gray-100 dark:bg-gray-900 rounded-md">
            <h3 className="font-medium mb-2">ID Information</h3>
            <div className="space-y-2 text-sm">
              <div className="grid grid-cols-2">
                <span className="font-medium">Date of Birth:</span>
                <span>{idInfo.dateOfBirth?.toLocaleDateString('en-ZA')}</span>
              </div>
              <div className="grid grid-cols-2">
                <span className="font-medium">Age:</span>
                <span>{idInfo.age} years</span>
              </div>
              <div className="grid grid-cols-2">
                <span className="font-medium">Gender:</span>
                <span className="capitalize">{idInfo.gender}</span>
              </div>
              <div className="grid grid-cols-2">
                <span className="font-medium">Citizenship:</span>
                <span>{idInfo.isCitizen ? 'South African Citizen' : 'Permanent Resident'}</span>
              </div>
              <div className="grid grid-cols-2">
                <span className="font-medium">Sequence Number:</span>
                <span>{idInfo.sequenceNumber}</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
